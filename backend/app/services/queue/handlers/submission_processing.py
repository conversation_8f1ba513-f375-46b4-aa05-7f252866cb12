"""
Submission Processing Job Handler

This module handles the complete submission processing workflow including:
- Exclusion filter checking
- Thesis matching and scoring
- Deal creation
- Enrichment job queuing
"""

from typing import Any, Dict

from app.core.logging import get_logger
from app.models.queue import Job, JobStatus
from app.services.factory import (
    get_job_service,
    get_submission_processing_service,
)
from app.services.job.interfaces import JobServiceInterface
from app.services.queue.worker_interface import JobHandlerInterface
from app.services.submission_processing.interfaces import (
    SubmissionProcessingServiceInterface,
)

logger = get_logger(__name__)


class SubmissionProcessingHandler(JobHandlerInterface):
    """Handler for submission processing jobs."""

    def __init__(self):
        self.submission_processing_service: Optional[
            SubmissionProcessingServiceInterface
        ] = None
        self.job_service: Optional[JobServiceInterface] = None
        self.logger = logger

    async def initialize(self) -> None:
        """Initialize handler dependencies."""
        self.submission_processing_service = await get_submission_processing_service()
        self.job_service = await get_job_service()

        await self.submission_processing_service.initialize()
        await self.job_service.initialize()

    async def cleanup(self) -> None:
        """Cleanup handler resources."""
        if self.submission_processing_service:
            await self.submission_processing_service.cleanup()
        if self.job_service:
            await self.job_service.cleanup()

    async def handle(self, job: Job) -> Dict[str, Any]:
        """Handle a submission processing job (JobHandlerInterface method)."""
        result = await self.process(job)
        return result or {"success": False, "error": "No result returned"}

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a submission processing job.

        Args:
            job: The job to process

        Returns:
            Processing result
        """
        payload = job.payload
        self.logger.info(f"Processing submission processing job: {job.id}")

        # Extract data from payload
        submission_id = payload.get("submission_id")
        form_id = payload.get("form_id")
        org_id = payload.get("org_id")
        answers = payload.get("answers")
        metadata = payload.get("metadata", {})
        created_by = payload.get("created_by")
        tracked_job_id = payload.get("tracked_job_id")

        self.logger.debug(
            f"Extracted job data: submission_id={submission_id}, form_id={form_id}, org_id={org_id}, tracked_job_id={tracked_job_id}"
        )

        if not all([submission_id, form_id, org_id, answers]):
            self.logger.error(
                "Missing required fields in payload", extra={"payload": payload}
            )
            raise ValueError("Missing required fields in payload")

        # Initialize services if not already done
        if not self.submission_processing_service:
            await self.initialize()

        try:
            # Update tracked job status to in progress
            if tracked_job_id:
                try:
                    self.logger.debug(
                        f"Updating job status to in progress for {tracked_job_id}"
                    )
                    await self.job_service.update_job_status(
                        job_id=tracked_job_id,
                        status=JobStatus.IN_PROGRESS,
                        progress=0.1,
                    )
                except Exception as e:
                    self.logger.error(
                        f"Failed to update job status: {str(e)}", exc_info=True
                    )

            # Process the submission through the complete workflow
            self.logger.info(f"Starting submission processing for {submission_id}")
            result = await self.submission_processing_service.process_submission(
                submission_id=submission_id,
                form_id=form_id,
                org_id=org_id,
                answers=answers,
                metadata=metadata,
                created_by=created_by,
            )

            # Update tracked job with results
            if tracked_job_id:
                try:
                    if result.success:
                        self.logger.debug(
                            f"Updating job status to completed for {tracked_job_id}"
                        )
                        await self.job_service.update_job_status(
                            job_id=tracked_job_id,
                            status=JobStatus.COMPLETED,
                            progress=1.0,
                            result=result.to_dict(),
                        )
                    else:
                        self.logger.debug(
                            f"Updating job status to failed for {tracked_job_id}"
                        )
                        await self.job_service.update_job_status(
                            job_id=tracked_job_id,
                            status=JobStatus.FAILED,
                            error=result.error,
                        )
                except Exception as e:
                    self.logger.error(
                        f"Failed to update job status: {str(e)}", exc_info=True
                    )

            if result.success:
                self.logger.info(
                    f"Successfully processed submission {submission_id}, created deal {result.deal_id}"
                )
                return {
                    "success": True,
                    "submission_id": submission_id,
                    "deal_id": result.deal_id,
                    "excluded": result.excluded,
                    "exclusion_reason": result.exclusion_reason,
                    "matching_theses": result.matching_theses,
                    "enrichment_job_id": result.enrichment_job_id,
                }
            else:
                self.logger.error(
                    f"Failed to process submission {submission_id}: {result.error}"
                )
                return {
                    "success": False,
                    "submission_id": submission_id,
                    "error": result.error,
                }

        except Exception as e:
            self.logger.error(
                f"Error processing submission {submission_id}: {str(e)}", exc_info=True
            )

            # Update tracked job status to failed
            if tracked_job_id:
                try:
                    await self.job_service.update_job_status(
                        job_id=tracked_job_id, status=JobStatus.FAILED, error=str(e)
                    )
                except Exception as job_error:
                    self.logger.error(
                        f"Failed to update job status for failed submission: {str(job_error)}",
                        exc_info=True,
                    )

            raise


def create_submission_processing_handler() -> SubmissionProcessingHandler:
    """Create a new submission processing handler instance."""
    logger.debug("Creating new SubmissionProcessingHandler instance")
    return SubmissionProcessingHandler()


# Register handlers
HANDLERS = {
    "process_submission": create_submission_processing_handler,
    "submission_processing": create_submission_processing_handler,
}
